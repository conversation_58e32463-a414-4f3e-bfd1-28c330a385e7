<template>
  <!-- <DynamicTable :columns="tableColumns" :tableData="tableData" border class="schedule-dept" show-summary
    :summary-method="getSummaries" /> -->
  <baseTable
    ref="mainTable"
    :tableColumn="tableColumns"
    :tableData="tableData"
    :height="550"
    :showOverflow="true"
    align="right"
    headerAlign="center"
    highlightCurrentRow
    showFooter
    :footerMethod="getSummaries"
    class="schedule-dept"
  ></baseTable>
</template>

<script>
import baseTable from "@/components/vxeTable/baseTable.vue";
export default {
  name: "ScheduleDept",
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  components: {
    baseTable,
  },
  data() {
    return {
      tableColumns: [
        {
          label: '序号',
          type: 'seq',
          width: '80',
          align: 'center',
          fixed: 'left'
        },
        {
          label: '部门',
          prop: 'deptName',
          width: '200',
          align: 'left',
        },
        {
          label: '经费卡预算总额(元)',
          prop: 'selfPurchaseType',
          width: '180',
          formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
          align: 'right'
        },
        {
          label: '经费卡已申请金额(元)',
          prop: 'amount',
          width: '180',
          formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
          align: 'right'
        },
        {
          label: '未启动',
          align: 'center',
          children: [
            {
              label: '项目数量',
              prop: '1',
              align: 'center'
            },
            {
              label: '预算金额(元)',
              prop: '2',
              width: '180',
              formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
              align: 'right'
            }
          ]
        },
        {
          label: '采购中',
          align: 'center',
          children: [
            {
              label: '项目数量',
              prop: '3',
              align: 'center'
            },
            {
              label: '预算金额(元)',
              prop: '4',
              width: '180',
              formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
              align: 'right'
            }
          ]
        },
        {
          label: '已成交',
          align: 'center',
          children: [
            {
              label: '项目数量',
              prop: '5',
              align: 'center'
            },
            {
              label: '成交金额(元)',
              prop: '6',
              width: '180',
              formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
              align: 'right'
            }
          ]
        },
        {
          label: '流标/废标',
          align: 'center',
          children: [
            {
              label: '项目数量',
              prop: '7',
              align: 'center'
            },
            {
              label: '预算金额(元)',
              prop: '8',
              width: '180',
              formatter: ({ cellValue }) => this.formatMoney(null, null, cellValue),
              align: 'right'
            }
          ]
        }
      ],
    }
  },
  methods: {
    formatMoney(row, column, cellValue) {
      return this.$formatMoney(cellValue);
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 1) {
          sums[index] = '合计';
          return;
        }
        if (!column.property
        ) {
          sums[index] = "";
          return;
        }
        // 从 data 中计算当前列的合计
        const sum = data.reduce((acc, item) => {
          const value = parseFloat(item[column.property
          ]);
          return isNaN(value) ? acc : acc + value;
        }, 0);
        const label = column.title;
        if (label.indexOf("额") !== -1) {
          // 金额、面积格式化
          sums[index] = this.$formatMoney(sum);
        } else if(label.indexOf("数") !== -1) {
          sums[index] = sum;
        } else {
          sums[index] = "";
        }
      });
      return [sums];
    }
  }
}
</script>

<style lang="scss" scoped></style>
