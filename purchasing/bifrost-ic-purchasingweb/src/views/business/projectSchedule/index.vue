<template>
  <div>
    <page ref="situationPage">
      <template #pageContent>
        <LayoutTem :isPageShow="false" :isFilterShow="false">
          <template #button>
            <div class="title-content">
              <div class="title">{{ tableTitle }}</div>
              <div class="description">
                说明：
                <div style="text-indent: 2em; line-height: 1.5;">1.未启动，指的是已建立采购项目，但未开始招标或采购的项目；</div>
                <div style="text-indent: 2em; line-height: 1.5;">2.采购中，指的是已开始招标，或采购的项目；</div>
                <div style="text-indent: 2em; line-height: 1.5;">3.已成交，指的是已签发中标通知书的招标项目或已完成采购流程的自行采购项目；</div>
                <div style="text-indent: 2em; line-height: 1.5;">4.流标/废标，指的招标项目的状态为流标或废标的项目；</div>
              </div>
            </div>
            <div class="search-content">
              <searchForm
                ref="searchForm"
                :formOptions="formOptions"
                @onSearch="handleSearch"
                @selectChange="handleSelectChange"
                :leftviewSpan="10"
                @onReset="onReset"
              />
            </div>
          </template>
          <template #main>
            <ScheduleAll v-if="scopeInAll" :tableData="allTableData" />
            <ScheduleDept v-if="scopeInDept" :tableData="tableData" />
          </template>
        </LayoutTem>
      </template>
    </page>
  </div>
</template>

<script>
import searchForm from '../reportSummer/regionSearch/searchForm.vue';
import ScheduleAll from './components/scheduleAll.vue';
import ScheduleDept from './components/scheduleDept.vue';
export default {
  name: "ProjectSchedule",
  components: {
    searchForm,
    ScheduleAll,
    ScheduleDept,
  },
  data() {
    return {
      formOptions: [
        {
          label: '年度',
          element: 'el-select',
          prop: 'year',
          initValue: new Date().getFullYear().toString(),
          rowSpan: 12,
          options: (() => {
            const currentYear = new Date().getFullYear();
            const options = [];
            for (let i = currentYear - 10; i <= currentYear + 10; i++) {
              const year = i.toString();
              options.push({ label: year, value: year });
            }
            return options;
          })()
        },
        {
          label: '统计范围',
          element: 'el-select',
          prop: 'queryScope',
          rowSpan: 12,
          initValue: '全校',
          options: [
            { label: '全校', value: '全校' },
            { label: '各部门', value: '各部门' },
          ]
        }
      ],
      formQuery: {
        year:  new Date().getFullYear().toString(),
        queryScope: '全校'
      },
      allTableData: [],
      tableData: []
    }
  },
  computed: {
    tableTitle() {
      return this.formQuery.queryScope === '全校' ? '深圳大学采购进度统计表' : '深圳大学各院系、部门采购进度统计表'
    },
    scopeInAll() {
      return this.formQuery.queryScope === '全校'
    },
    scopeInDept() {
      return this.formQuery.queryScope === '各部门'
    }
  },
  watch: {
    // 'formQuery': {
    //   // handler(val) {
    //   //   if (val.queryScope === '各部门') {
    //   //     this.tableData = xmjdData
    //   //   }
    //   // },
    //   deep: true,
    //   immediate: true
    // }
  },
  mounted() { 
    this.getData()
  },
  methods: {
    handleSearch(obj) {
      this.formQuery.year = obj.year
      this.getDeptData()
    },
    handleSelectChange({ value, prop }) {
      // 处理下拉选择变化
      this.formQuery[prop] = value
    },
    getData() {
        this.$callApiParams(
          "queryPurchaseProgressStatistics",
          { year: this.formQuery.year },
          (res) => {
            this.allTableData = res.data.deptList
            this.tableData = res.data.summary
            return true;
          },
          (error) => {
            reject(error);
          }
        );
    },
    onReset(obj) {
      this.getData()
    }
  }
}
</script>

<style lang="scss" scoped>
.title-content {
  width: 100%;
  text-align: center;
  margin: 20px 0;

  .title {
    font-size: 20px;
  }

  .description {
    margin-bottom: 20px;
    width: 100%;
    text-align: start;
    padding-top: 10px;
    font-size: 14px;
    color: #666;
  }
}
</style>
