<template>
  <div>
    <page ref="situationPage">
      <template #pageContent>
        <LayoutTem :isPageShow="false" :isFilterShow="false">
          <template #button>
            <div class="title-content">
              <div class="title">{{ tableTitle }}</div>
              <div class="description">说明：本统计表以所有已成交项目为基础进行的统计</div>
            </div>
            <div class="search-content">
              <searchForm ref="searchForm" :formOptions="formOptions" @onSearch="handleSearch"
                @selectChange="handleSelectChange"  @onReset="onReset" :leftviewSpan="10" />
            </div>
          </template>
          <template #main>
            <template v-if="formQuery.type === '按采购标的分类统计'">
              <ProcurementSubjectAll v-if="scopeInAll" :tableData="tableData" />
              <ProcurementSubjectDept v-if="scopeInDept" :tableData="tableData" />
            </template>
            <template v-if="formQuery.type === '按采购组织形式统计'">
              <procurementOrganizationAll v-if="scopeInAll" :tableData="tableData" />
              <procurementOrganizationDept v-if="scopeInDept" :tableData="tableData" />
            </template>
            <template v-if="formQuery.type === '按采购执行机构统计'">
              <procurementExecutionAll v-if="scopeInAll" :tableData="tableData" />
              <procurementExecutionDept v-if="scopeInDept" :tableData="tableData" />
            </template>
            <template v-if="formQuery.type === '按采购方式统计'">
              <procurementMethodAll v-if="scopeInAll" :tableData="tableData" />
              <procurementMethodDept v-if="scopeInDept" :tableData="tableData" />
            </template>
          </template>
        </LayoutTem>
      </template>
    </page>
  </div>
</template>

<script>
import searchForm from '../reportSummer/regionSearch/searchForm.vue';
import ProcurementSubjectAll from './components/procurementSubjectAll.vue';
import ProcurementSubjectDept from './components/procurementSubjectDept.vue';
import procurementOrganizationAll from './components/procurementOrganizationAll.vue';
import procurementOrganizationDept from './components/procurementOrganizationDept.vue';
import procurementExecutionAll from './components/procurementExecutionAll.vue';
import procurementExecutionDept from './components/procurementExecutionDept.vue';
import procurementMethodAll from './components/procurementMethodAll.vue';
import procurementMethodDept from './components/procurementMethodDept.vue';
export default {
  name: 'BusinessSituationTable',
  components: {
    searchForm,
    ProcurementSubjectAll,
    ProcurementSubjectDept,
    procurementOrganizationAll,
    procurementOrganizationDept,
    procurementExecutionAll,
    procurementExecutionDept,
    procurementMethodAll,
    procurementMethodDept,
  },
  data() {
    return {
      currentTab: '按采购标的分类统计',
      formOptions: [
        {
          label: '年度',
          element: 'el-select',
          prop: 'year',
          initValue: new Date().getFullYear().toString(),
          rowSpan: 8,
          options: (() => {
            const currentYear = new Date().getFullYear();
            const options = [];
            for (let i = currentYear - 10; i <= currentYear + 10; i++) {
              const year = i.toString();
              options.push({ label: year, value: year });
            }
            return options;
          })()
        },
        {
          label: '统计范围',
          element: 'el-select',
          prop: 'range',
          initValue: '全校',
          rowSpan: 8,
          options: [
            { label: '全校', value: '全校' },
            { label: '各部门', value: '各部门' },
          ]
        },
        {
          label: '查询方式',
          element: 'el-select',
          prop: 'type',
          rowSpan: 8,
          initValue: '按采购标的分类统计',
          options: [
            { label: '按采购标的分类统计', value: '按采购标的分类统计' },
            { label: '按采购组织形式统计', value: '按采购组织形式统计' },
            { label: '按采购执行机构统计', value: '按采购执行机构统计' },
            { label: '按采购方式统计', value: '按采购方式统计' },
          ]
        }
      ],
      formQuery: {
        year:  new Date().getFullYear().toString(),
        range: '全校',
        type: '按采购标的分类统计'
      },
      allTableData: [],
      tableData: [], 
      type: '',
      range: '',
    }
  },
  computed: {
    tableTitle() {
      return this.formQuery.range === '全校' ? '深圳大学采购项目成交情况表' : '深圳大学各院系、部门采购项目成交情况表'
    },
    scopeInAll() {
      return this.formQuery.range === '全校'
    },
    scopeInDept() {
      return this.formQuery.range === '各部门'
    }
  },
  watch: {
    'formQuery.type': {
      handler(val) {
          if (val === '按采购标的分类统计') {
            this.type = 1
          } else if (val === '按采购组织形式统计') {
            this.type = 2
          } else if (val === '按采购执行机构统计') {
            this.type = 3
          } else if (val === '按采购方式统计') {
            this.type = 4
          }
          console.log(this.type, 'types')
      },
      deep: true,
      immediate: true
    },
    'formQuery.range': {
      handler(val) {
          if (val === '全校') {
            this.range = 'summary'
          } else if (val === '各部门') {
            this.range = 'dept'
          } 
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    changeTab(tab) {
      this.currentTab = tab.name;
    },
    handleSearch(obj) {
      this.getData()
    },
    handleSelectChange({ value, prop }) {
      console.log(value, prop);
      // 处理下拉选择变化
      this.formQuery[prop] = value
    },
     getData() {
        this.$callApiParams(
          "queryProjectConclude",
          { year: this.formQuery.year, range: this.range, type: this.type },
          (res) => {
            this.tableData = res.data
            return true;
          },
          (error) => {
            reject(error);
          }
        );
    },
    onReset(obj) {
      this.getData()
    }
  },
  mounted() {
    this.getData()
  },
}
</script>

<style lang="scss" scoped>
.title-content {
  width: 100%;
  text-align: center;
  margin: 20px 0;

  .title {
    font-size: 20px;
  }

  .description {
    margin-bottom: 20px;
    width: 100%;
    text-align: start;
    padding-top: 10px;
    font-size: 14px;
    color: #666;
  }
}
</style>
